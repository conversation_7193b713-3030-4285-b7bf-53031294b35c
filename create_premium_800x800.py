#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
顶级美化封面图生成器 - 800x800像素
最高等级的视觉效果，包含3D效果、光晕、渐变等高级特效
"""

from PIL import Image, ImageDraw, ImageFont, ImageFilter
import math
import os
from datetime import datetime

def get_chinese_font(size):
    """获取支持中文的字体"""
    font_paths = [
        "C:/Windows/Fonts/msyh.ttc",      # 微软雅黑
        "C:/Windows/Fonts/simhei.ttf",    # 黑体
        "C:/Windows/Fonts/simsun.ttc",    # 宋体
    ]
    
    for font_path in font_paths:
        try:
            if os.path.exists(font_path):
                return ImageFont.truetype(font_path, size)
        except:
            continue
    
    try:
        return ImageFont.truetype("arial.ttf", size)
    except:
        return ImageFont.load_default()

def create_radial_gradient(width, height, center_color, edge_color):
    """创建径向渐变"""
    img = Image.new('RGBA', (width, height), (0, 0, 0, 0))
    
    center_x, center_y = width // 2, height // 2
    max_radius = math.sqrt(center_x**2 + center_y**2)
    
    for y in range(height):
        for x in range(width):
            distance = math.sqrt((x - center_x)**2 + (y - center_y)**2)
            ratio = min(distance / max_radius, 1.0)
            
            # 平滑插值
            r = int(center_color[0] * (1 - ratio) + edge_color[0] * ratio)
            g = int(center_color[1] * (1 - ratio) + edge_color[1] * ratio)
            b = int(center_color[2] * (1 - ratio) + edge_color[2] * ratio)
            a = int(center_color[3] * (1 - ratio) + edge_color[3] * ratio)
            
            img.putpixel((x, y), (r, g, b, a))
    
    return img

def add_glow_effect(draw, x, y, width, height, color, intensity=5):
    """添加发光效果"""
    for i in range(intensity, 0, -1):
        alpha = 30 - i * 5
        glow_color = tuple(list(color) + [alpha])
        draw.rounded_rectangle([x-i*2, y-i*2, x+width+i*2, y+height+i*2], 
                              radius=15+i, outline=glow_color, width=2)

def create_premium_cover():
    """创建顶级美化封面图"""
    width, height = 800, 800
    
    # 创建基础画布
    img = Image.new('RGBA', (width, height), (0, 0, 0, 255))
    
    # 创建复杂背景层
    # 第一层：深色基础
    bg1 = Image.new('RGBA', (width, height), (8, 12, 25, 255))
    
    # 第二层：径向渐变
    bg2 = create_radial_gradient(width, height, (25, 40, 70, 200), (8, 12, 25, 255))
    
    # 第三层：对角渐变
    bg3 = Image.new('RGBA', (width, height), (0, 0, 0, 0))
    for y in range(height):
        for x in range(width):
            ratio = (x + y) / (width + height)
            alpha = int(50 * math.sin(ratio * math.pi))
            bg3.putpixel((x, y), (15, 30, 60, alpha))
    
    # 合成背景
    img = Image.alpha_composite(img, bg1)
    img = Image.alpha_composite(img, bg2)
    img = Image.alpha_composite(img, bg3)
    
    # 添加网格效果
    grid_overlay = Image.new('RGBA', (width, height), (0, 0, 0, 0))
    grid_draw = ImageDraw.Draw(grid_overlay)
    
    grid_size = 30
    for x in range(0, width, grid_size):
        grid_draw.line([(x, 0), (x, height)], fill=(80, 120, 200, 20), width=1)
    for y in range(0, height, grid_size):
        grid_draw.line([(0, y), (width, y)], fill=(80, 120, 200, 20), width=1)
    
    img = Image.alpha_composite(img, grid_overlay)
    
    # 转换为RGB进行绘制
    final_img = Image.new('RGB', (width, height), (8, 12, 25))
    final_img.paste(img, (0, 0), img)
    draw = ImageDraw.Draw(final_img)
    
    # 获取字体
    title_font = get_chinese_font(42)
    subtitle_font = get_chinese_font(20)
    tech_font = get_chinese_font(18)
    small_font = get_chinese_font(14)
    
    print("开始绘制顶级美化效果...")
    
    # 主标题区域 - 3D效果
    main_title = "专业软件开发"
    title_bbox = draw.textbbox((0, 0), main_title, font=title_font)
    title_width = title_bbox[2] - title_bbox[0]
    title_x = (width - title_width) // 2
    title_y = 50
    
    # 标题3D阴影效果
    for offset in range(5, 0, -1):
        shadow_alpha = 100 - offset * 15
        draw.text((title_x + offset, title_y + offset), main_title, 
                 fill=(0, 0, 0, shadow_alpha), font=title_font)
    
    # 标题发光背景
    for i in range(8, 0, -1):
        glow_alpha = 40 - i * 4
        draw.rounded_rectangle([title_x-30-i*3, title_y-15-i*2, 
                               title_x+title_width+30+i*3, title_y+50+i*2], 
                              radius=20+i*2, fill=(0, 150, 255, glow_alpha))
    
    # 标题主背景 - 渐变效果
    draw.rounded_rectangle([title_x-30, title_y-15, title_x+title_width+30, title_y+50], 
                          radius=20, fill=(20, 80, 180), outline=(100, 200, 255), width=3)
    
    # 标题文字
    draw.text((title_x, title_y), main_title, fill='white', font=title_font)
    
    # 副标题
    subtitle = "Professional Software Development"
    subtitle_bbox = draw.textbbox((0, 0), subtitle, font=subtitle_font)
    subtitle_width = subtitle_bbox[2] - subtitle_bbox[0]
    subtitle_x = (width - subtitle_width) // 2
    draw.text((subtitle_x, 120), subtitle, fill='#B0C4DE', font=subtitle_font)
    
    # 技术服务卡片 - 高级3D效果
    tech_services = [
        ("软件开发", "#FF6B35", (80, 170)),
        ("脚本定制", "#4169E1", (420, 170)),
        ("Windows开发", "#32CD32", (80, 240)),
        ("网站开发", "#FF1493", (420, 240)),
        ("App开发", "#9932CC", (80, 310)),
        ("嵌入式开发", "#FF8C00", (420, 310)),
        ("STM32开发", "#DC143C", (250, 380))
    ]
    
    for service, color, (x, y) in tech_services:
        color_rgb = tuple(int(color[i:i+2], 16) for i in (1, 3, 5))
        
        text_bbox = draw.textbbox((0, 0), service, font=tech_font)
        text_width = text_bbox[2] - text_bbox[0]
        card_width = text_width + 50
        card_height = 45
        
        # 卡片3D阴影
        for depth in range(6, 0, -1):
            shadow_color = tuple([int(c * 0.3) for c in color_rgb])
            draw.rounded_rectangle([x+depth, y+depth, x+card_width+depth, y+card_height+depth], 
                                  radius=12, fill=shadow_color)
        
        # 卡片发光效果
        for glow in range(5, 0, -1):
            glow_alpha = 60 - glow * 10
            glow_color = tuple(list(color_rgb) + [glow_alpha])
            draw.rounded_rectangle([x-glow*2, y-glow*2, x+card_width+glow*2, y+card_height+glow*2], 
                                  radius=12+glow, outline=glow_color, width=2)
        
        # 卡片渐变背景
        for i in range(card_height):
            ratio = i / card_height
            grad_color = tuple([int(c * (0.8 + 0.4 * ratio)) for c in color_rgb])
            draw.rectangle([x, y+i, x+card_width, y+i+1], fill=grad_color)
        
        # 卡片边框
        draw.rounded_rectangle([x, y, x+card_width, y+card_height], 
                              radius=12, outline=(255, 255, 255), width=2)
        
        # 卡片高光
        draw.rounded_rectangle([x+2, y+2, x+card_width-2, y+15], 
                              radius=10, fill=(255, 255, 255, 50))
        
        # 文字阴影
        text_x = x + (card_width - text_width) // 2
        text_y = y + (card_height - (text_bbox[3] - text_bbox[1])) // 2
        draw.text((text_x+2, text_y+2), service, fill=(0, 0, 0, 100), font=tech_font)
        draw.text((text_x, text_y), service, fill='white', font=tech_font)
    
    # 特色功能 - 发光标签
    features = ["✓ 专业团队", "✓ 快速交付", "✓ 质量保证", "✓ 技术支持", "✓ 合理价格", "✓ 定制方案"]
    
    feature_positions = [
        (130, 470), (400, 470), (670, 470),
        (130, 510), (400, 510), (670, 510)
    ]
    
    for i, (feature, (x, y)) in enumerate(zip(features, feature_positions)):
        feature_bbox = draw.textbbox((0, 0), feature, font=small_font)
        feature_width = feature_bbox[2] - feature_bbox[0]
        
        # 特色标签发光效果
        for glow in range(4, 0, -1):
            glow_alpha = 40 - glow * 8
            draw.rounded_rectangle([x - feature_width//2 - 12 - glow, y - 8 - glow, 
                                   x + feature_width//2 + 12 + glow, y + 20 + glow], 
                                  radius=8+glow, fill=(0, 100, 200, glow_alpha))
        
        # 特色标签背景
        draw.rounded_rectangle([x - feature_width//2 - 12, y - 8, 
                               x + feature_width//2 + 12, y + 20], 
                              radius=8, fill=(0, 80, 160), outline=(100, 200, 255), width=2)
        
        # 特色标签高光
        draw.rounded_rectangle([x - feature_width//2 - 10, y - 6, 
                               x + feature_width//2 + 10, y + 2], 
                              radius=6, fill=(255, 255, 255, 30))
        
        draw.text((x - feature_width//2, y), feature, fill='#87CEEB', font=small_font)
    
    # 联系信息区域 - 豪华效果
    contact_y = 580
    contact_text = "专业开发团队 · 一站式解决方案"
    contact_bbox = draw.textbbox((0, 0), contact_text, font=subtitle_font)
    contact_width = contact_bbox[2] - contact_bbox[0]
    contact_x = (width - contact_width) // 2
    
    # 联系信息发光背景
    for glow in range(8, 0, -1):
        glow_alpha = 50 - glow * 5
        draw.rounded_rectangle([contact_x-40-glow*2, contact_y-20-glow, 
                               contact_x+contact_width+40+glow*2, contact_y+35+glow], 
                              radius=15+glow, fill=(20, 60, 120, glow_alpha))
    
    # 联系信息主背景
    draw.rounded_rectangle([contact_x-40, contact_y-20, contact_x+contact_width+40, contact_y+35], 
                          radius=15, fill=(30, 70, 140), outline=(150, 220, 255), width=3)
    
    # 联系信息高光
    draw.rounded_rectangle([contact_x-35, contact_y-15, contact_x+contact_width+35, contact_y-5], 
                          radius=12, fill=(255, 255, 255, 40))
    
    draw.text((contact_x, contact_y), contact_text, fill='white', font=subtitle_font)
    
    # 底部信息
    current_time = datetime.now().strftime("%Y年%m月")
    footer_text = f"© {current_time} 专业软件开发服务"
    footer_bbox = draw.textbbox((0, 0), footer_text, font=small_font)
    footer_width = footer_bbox[2] - footer_bbox[0]
    footer_x = (width - footer_width) // 2
    
    draw.text((footer_x, height - 50), footer_text, fill='#708090', font=small_font)
    
    # 装饰性光效
    # 左上角光束
    for i in range(20):
        alpha = 100 - i * 4
        draw.polygon([(0, 0), (150-i*3, 0), (0, 150-i*3)], fill=(0, 150, 255, alpha))
    
    # 右下角光束
    for i in range(20):
        alpha = 80 - i * 3
        draw.polygon([(width, height), (width-120+i*2, height), (width, height-120+i*2)], 
                    fill=(255, 100, 50, alpha))
    
    # 粒子效果
    particle_positions = [
        (150, 650), (650, 650), (150, 720), (650, 720),
        (100, 600), (700, 600), (400, 680)
    ]
    
    for x, y in particle_positions:
        # 粒子发光
        for size in range(15, 0, -1):
            alpha = 80 - size * 4
            draw.ellipse([x-size, y-size, x+size, y+size], fill=(100, 200, 255, alpha))
        
        # 粒子核心
        draw.ellipse([x-8, y-8, x+8, y+8], fill=(200, 230, 255), outline=(255, 255, 255), width=2)
        draw.ellipse([x-4, y-4, x+4, y+4], fill='white')
    
    # 保存图片
    output_path = 'premium_software_cover_800x800.png'
    final_img.save(output_path, 'PNG', quality=100)
    
    print(f"顶级美化封面图已生成: {output_path}")
    print(f"图片尺寸: {width}x{height} 像素")
    
    return output_path

if __name__ == "__main__":
    try:
        print("开始创建顶级美化封面图...")
        cover_path = create_premium_cover()
        print(f"\n✅ 顶级美化封面图创建成功!")
        print(f"📁 文件位置: {os.path.abspath(cover_path)}")
        print("\n🎨 顶级美化特效:")
        print("  • 3D立体阴影效果")
        print("  • 多层发光光晕")
        print("  • 径向渐变背景")
        print("  • 粒子光效装饰")
        print("  • 高光和反射效果")
        print("  • 专业配色方案")
        print("  • 完美的中文字体显示")
    except Exception as e:
        print(f"❌ 创建封面图时出错: {e}")
        import traceback
        traceback.print_exc()
